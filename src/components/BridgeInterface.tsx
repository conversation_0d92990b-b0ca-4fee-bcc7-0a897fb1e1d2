"use client";
import { useWallet } from "@solana/wallet-adapter-react";
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useConnect, useDisconnect } from "wagmi";
import { oft } from "@layerzerolabs/oft-v2-solana-sdk";
import { useState, useEffect, useCallback, useMemo } from "react";
import { EndpointId } from "@layerzerolabs/lz-definitions";
import { publicKey, transactionBuilder } from "@metaplex-foundation/umi";
import { createUmi } from "@metaplex-foundation/umi-bundle-defaults";
import { addressToBytes32 } from "@layerzerolabs/lz-v2-utilities";
import { walletAdapterIdentity } from "@metaplex-foundation/umi-signer-wallet-adapters";
import { Connection, PublicKey } from "@solana/web3.js";
import { getAssociatedTokenAddress, getAccount, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { fromWeb3JsPublicKey } from '@metaplex-foundation/umi-web3js-adapters';
import { findAssociatedTokenPda, createSplAssociatedTokenProgram, setComputeUnitLimit, setComputeUnitPrice } from '@metaplex-foundation/mpl-toolbox';
import bs58 from 'bs58';
import TransactionStatus from './TransactionStatus';
import { useReadContract } from 'wagmi';
import { erc20Abi, parseUnits } from 'viem';
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui";

// Environment variables
const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com";
const SOLANA_OFT_MINT_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS;
const SOLANA_ESCROW_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS;
const SOLANA_PROGRAM_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS;
const ETHEREUM_OFT_ADDRESS = process.env.NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS;
const DEFAULT_AMOUNT = parseFloat(process.env.NEXT_PUBLIC_DEFAULT_BRIDGE_AMOUNT || "0.1");

// Mainnet endpoint IDs
const ETHEREUM_MAINNET_EID = EndpointId.ETHEREUM_V2_MAINNET;
const SOLANA_MAINNET_EID = EndpointId.SOLANA_V2_MAINNET;

// Token decimals
const SOLANA_TOKEN_DECIMALS = 6;
const ETHEREUM_TOKEN_DECIMALS = 18;

// LayerZero scan link utility
const getLayerZeroScanLink = (txHash: string, isTestnet: boolean = false): string => {
  const baseUrl = isTestnet ? 'https://testnet.layerzeroscan.com' : 'https://layerzeroscan.com';
  return `${baseUrl}/tx/${txHash}`;
};

// Gas price fetching utility
const fetchGasPrice = async (): Promise<{ maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null> => {
  try {
    const response = await fetch('https://api.blocknative.com/gasprices/blockprices?chainid=1');
    const data = await response.json();

    if (data.blockPrices && data.blockPrices.length > 0) {
      // Use 90% confidence level for good balance of speed and cost
      const estimatedPrices = data.blockPrices[0].estimatedPrices;
      const price90 = estimatedPrices.find((p: any) => p.confidence === 90);

      if (price90) {
        // Convert from gwei to wei
        const maxFeePerGas = BigInt(Math.ceil(price90.maxFeePerGas * 1e9));
        const maxPriorityFeePerGas = BigInt(Math.ceil(price90.maxPriorityFeePerGas * 1e9));

        return { maxFeePerGas, maxPriorityFeePerGas };
      }
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch gas price:', error);
    return null;
  }
};

// Gas limit for LayerZero OFT transactions
const ETHEREUM_GAS_LIMIT = BigInt(250000);

// Address validation utilities
const isValidEthereumAddress = (address: string): boolean => {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
};

const isValidSolanaAddress = (address: string): boolean => {
  try {
    // Solana addresses are base58 encoded and typically 32-44 characters
    const decoded = bs58.decode(address);
    return decoded.length === 32;
  } catch {
    return false;
  }
};

// OFT ABI for LayerZero OFT contracts
const oftAbi = [
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      { "internalType": "bool", "name": "_payInLzToken", "type": "bool" }
    ],
    "name": "quoteSend",
    "outputs": [
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "msgFee",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "_fee",
        "type": "tuple"
      },
      { "internalType": "address", "name": "_refundAddress", "type": "address" }
    ],
    "name": "send",
    "outputs": [
      {
        "components": [
          { "internalType": "bytes32", "name": "guid", "type": "bytes32" },
          { "internalType": "uint64", "name": "nonce", "type": "uint64" },
          {
            "components": [
              { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
              { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
            ],
            "internalType": "struct MessagingFee",
            "name": "fee",
            "type": "tuple"
          }
        ],
        "internalType": "struct MessagingReceipt",
        "name": "msgReceipt",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "amountSentLD", "type": "uint256" },
          { "internalType": "uint256", "name": "amountReceivedLD", "type": "uint256" }
        ],
        "internalType": "struct OFTReceipt",
        "name": "oftReceipt",
        "type": "tuple"
      }
    ],
    "stateMutability": "payable",
    "type": "function"
  }
] as const;

interface BridgeState {
  isLoading: boolean;
  error: string | null;
  txHash: string | null;
  nativeFee: bigint | null;
  receiveAmount: string | null;
  solanaBalance: string | null;
  ethereumBalance: string | null;
  layerZeroScanLink: string | null;
  gasPrice: { maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null;
  customEthAddress: string;
  customSolanaAddress: string;
}

interface Transaction {
  hash: string;
  timestamp: number;
  fromChain: 'solana' | 'ethereum';
  toChain: 'solana' | 'ethereum';
  amount: string;
  status: 'pending' | 'confirmed' | 'failed';
  layerZeroScanLink?: string;
}

// Ethereum Connect Button Component
function EthereumConnectButton() {
  const { connect, connectors, isPending } = useConnect();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className="bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-4 rounded-lg transition-colors"
      >
        Select Wallet
      </button>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-2xl p-6 max-w-sm w-full mx-4 border border-gray-700">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Connect Ethereum Wallet</h3>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-3">
              {connectors.map((connector) => (
                <button
                  key={connector.uid}
                  onClick={() => {
                    connect({ connector });
                    setIsModalOpen(false);
                  }}
                  disabled={isPending}
                  className="w-full flex items-center space-x-3 p-3 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors disabled:opacity-50"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {connector.name.charAt(0)}
                    </span>
                  </div>
                  <span className="text-white font-medium">{connector.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default function BridgeInterface() {
  const solanaWallet = useWallet();
  const { address: ethAddress, isConnected: isEthConnected } = useAccount();
  const { disconnect } = useDisconnect();

  const [isClient, setIsClient] = useState(false);
  const [amount, setAmount] = useState(DEFAULT_AMOUNT.toString());
  const [direction, setDirection] = useState<'sol-to-eth' | 'eth-to-sol'>('sol-to-eth');
  const [bridgeState, setBridgeState] = useState<BridgeState>({
    isLoading: false,
    error: null,
    txHash: null,
    nativeFee: null,
    receiveAmount: null,
    solanaBalance: null,
    ethereumBalance: null,
    layerZeroScanLink: null,
    gasPrice: null,
    customEthAddress: '',
    customSolanaAddress: '',
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  // Memoize UMI instance to prevent recreation on every render
  const umi = useMemo(() => {
    const umiInstance = createUmi(SOLANA_RPC_URL);
    if (solanaWallet.wallet) {
      umiInstance.use(walletAdapterIdentity(solanaWallet));
    }
    // Register the SPL Associated Token program
    umiInstance.programs.add(createSplAssociatedTokenProgram());
    return umiInstance;
  }, [solanaWallet]);

  // Fetch Ethereum token balance - only when connected
  const { data: ethereumBalanceRaw } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: erc20Abi,
    functionName: 'balanceOf',
    args: [ethAddress as `0x${string}`],
    query: {
      enabled: !!ethAddress && !!ETHEREUM_OFT_ADDRESS && isEthConnected,
    },
  });

  // Wagmi hooks for Ethereum contract interactions
  const { writeContract: writeOftContract, data: ethTxHash, isPending: isEthTxPending, error: ethTxError } = useWriteContract();
  const { isSuccess: isEthTxSuccess } = useWaitForTransactionReceipt({
    hash: ethTxHash,
  });

  // Quote Ethereum OFT contract
  const { data: ethQuoteData, refetch: refetchEthQuote } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: oftAbi,
    functionName: 'quoteSend',
    args: [
      {
        dstEid: SOLANA_MAINNET_EID,
        to: '0x0000000000000000000000000000000000000000000000000000000000000000', // Will be set dynamically
        amountLD: BigInt(0), // Will be set dynamically
        minAmountLD: BigInt(0), // Will be set dynamically
        extraOptions: '0x',
        composeMsg: '0x',
        oftCmd: '0x',
      },
      false // payInLzToken
    ],
    query: {
      enabled: false, // We'll trigger this manually
    },
  });

  // Memoized Solana balance fetcher
  const fetchSolanaBalance = useCallback(async () => {
    if (!solanaWallet.publicKey || !SOLANA_OFT_MINT_ADDRESS || !solanaWallet.connected) {
      return null;
    }

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mintPublicKey = new PublicKey(SOLANA_OFT_MINT_ADDRESS);
      const ownerPublicKey = new PublicKey(solanaWallet.publicKey.toString());

      // Get the associated token address
      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintPublicKey,
        ownerPublicKey
      );

      // Get the token account info
      const tokenAccountInfo = await getAccount(connection, associatedTokenAddress);
      const balance = Number(tokenAccountInfo.amount) / Math.pow(10, SOLANA_TOKEN_DECIMALS);
      return balance.toFixed(6);
    } catch (error) {
      console.error("Error fetching Solana balance:", error);
      return "0";
    }
  }, [solanaWallet.publicKey, solanaWallet.connected]);

  // Client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Update Solana balance when wallet connects/disconnects
  useEffect(() => {
    if (!isClient) return;

    if (solanaWallet.connected && solanaWallet.publicKey) {
      fetchSolanaBalance().then(balance => {
        setBridgeState(prev => ({ ...prev, solanaBalance: balance }));
      });
    } else {
      // Clear balance when wallet disconnects
      setBridgeState(prev => ({ ...prev, solanaBalance: null }));
    }
  }, [isClient, solanaWallet.connected, solanaWallet.publicKey, fetchSolanaBalance]);

  // Update Ethereum balance when data changes
  useEffect(() => {
    if (!isClient) return;

    if (ethereumBalanceRaw && isEthConnected) {
      const ethBalance = (Number(ethereumBalanceRaw) / Math.pow(10, ETHEREUM_TOKEN_DECIMALS)).toFixed(6);
      setBridgeState(prev => ({ ...prev, ethereumBalance: ethBalance }));
    } else {
      // Clear balance when wallet disconnects
      setBridgeState(prev => ({ ...prev, ethereumBalance: null }));
    }
  }, [isClient, ethereumBalanceRaw, isEthConnected]);

  // Handle Ethereum transaction success
  useEffect(() => {
    if (ethTxHash && isEthTxSuccess) {
      const layerZeroScanLink = getLayerZeroScanLink(ethTxHash, false);

      // Add transaction to history
      const newTransaction: Transaction = {
        hash: ethTxHash,
        timestamp: Date.now(),
        fromChain: 'ethereum',
        toChain: 'solana',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash: ethTxHash,
        layerZeroScanLink,
        isLoading: false
      }));
    }
  }, [ethTxHash, isEthTxSuccess, amount]);

  // Handle Ethereum transaction pending state
  useEffect(() => {
    if (isEthTxPending) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));
    }
  }, [isEthTxPending]);

  // Handle Ethereum transaction errors
  useEffect(() => {
    if (ethTxError) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        error: ethTxError.message || "Transaction failed"
      }));
    }
  }, [ethTxError]);

  // All callback and memoized functions - moved before conditional return
  const resetBridgeState = useCallback(() => {
    setBridgeState(prev => ({
      ...prev,
      isLoading: false,
      error: null,
      txHash: null,
      nativeFee: null,
      receiveAmount: null,
      layerZeroScanLink: null,
      gasPrice: null,
    }));
  }, []);

  const validateInputs = useCallback(() => {
    if (!SOLANA_OFT_MINT_ADDRESS || !SOLANA_ESCROW_ADDRESS || !SOLANA_PROGRAM_ADDRESS || !ETHEREUM_OFT_ADDRESS) {
      throw new Error("Missing environment variables. Please check your .env.local file.");
    }

    if (direction === 'sol-to-eth') {
      if (!solanaWallet.connected || !solanaWallet.publicKey) {
        throw new Error("Please connect your Solana wallet first.");
      }
      // Check if user has connected Ethereum wallet OR entered a valid custom address
      if (!ethAddress && !bridgeState.customEthAddress) {
        throw new Error("Please connect your Ethereum wallet or enter a recipient address.");
      }
      if (bridgeState.customEthAddress && !isValidEthereumAddress(bridgeState.customEthAddress)) {
        throw new Error("Please enter a valid Ethereum address.");
      }
    } else {
      if (!isEthConnected || !ethAddress) {
        throw new Error("Please connect your Ethereum wallet first.");
      }
      // Check if user has connected Solana wallet OR entered a valid custom address
      if (!solanaWallet.publicKey && !bridgeState.customSolanaAddress) {
        throw new Error("Please connect your Solana wallet or enter a recipient address.");
      }
      if (bridgeState.customSolanaAddress && !isValidSolanaAddress(bridgeState.customSolanaAddress)) {
        throw new Error("Please enter a valid Solana address.");
      }
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      throw new Error("Please enter a valid amount.");
    }
  }, [direction, solanaWallet.connected, solanaWallet.publicKey, ethAddress, isEthConnected, amount, bridgeState.customEthAddress, bridgeState.customSolanaAddress]);

  const quoteSolanaToEthereum = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));

      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      const { nativeFee } = await oft.quote(
        umi.rpc,
        {
          payer: publicKey(solanaWallet.publicKey!),
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
        },
        {
          payInLzToken: false,
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
        }
      );

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Solana has 6 decimals, Ethereum has 18 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, ethAddress, solanaWallet.publicKey, umi]);

  const executeSolanaToEthereum = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteSolanaToEthereum();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));
      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      // Get token account
      const tokenAccount = findAssociatedTokenPda(umi, {
        mint: fromWeb3JsPublicKey(new PublicKey(SOLANA_OFT_MINT_ADDRESS!)),
        owner: publicKey(solanaWallet.publicKey!),
        tokenProgramId: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID),
      });

      const ix = await oft.send(
        umi.rpc,
        {
          payer: umi.identity,
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
          tokenSource: tokenAccount[0],
        },
        {
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
          nativeFee: bridgeState.nativeFee,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
          token: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID)
        }
      );

      // Add compute unit instructions to handle the complex LayerZero transaction
      const computeUnitLimitIx = setComputeUnitLimit(umi, { units: 400_000 });
      const computeUnitPriceIx = setComputeUnitPrice(umi, { microLamports: 1000 });

      const txB = transactionBuilder()
        .add(computeUnitLimitIx)
        .add(computeUnitPriceIx)
        .add([ix]);
      const { signature } = await txB.sendAndConfirm(umi);
      const txHash = bs58.encode(signature);
      const layerZeroScanLink = getLayerZeroScanLink(txHash, false);

      // Add transaction to history
      const newTransaction: Transaction = {
        hash: txHash,
        timestamp: Date.now(),
        fromChain: 'solana',
        toChain: 'ethereum',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash,
        layerZeroScanLink,
        isLoading: false
      }));

      // Refresh balance after successful transaction
      if (solanaWallet.connected && solanaWallet.publicKey) {
        const newBalance = await fetchSolanaBalance();
        setBridgeState(prev => ({ ...prev, solanaBalance: newBalance }));
      }
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteSolanaToEthereum, amount, ethAddress, solanaWallet.publicKey, umi, fetchSolanaBalance, solanaWallet.connected]);

  const quoteEthereumToSolana = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false
      }));
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert amount to proper decimals for Ethereum (18 decimals)
      const amountInTokens = parseUnits(amount, ETHEREUM_TOKEN_DECIMALS);

      // Use custom address if provided, otherwise use connected wallet address
      const recipientSolanaAddress = bridgeState.customSolanaAddress || solanaWallet.publicKey!.toString();
      const solanaAddressBytes = bs58.decode(recipientSolanaAddress);
      const recipientAddressBytes32 = `0x${Buffer.from(solanaAddressBytes).toString('hex').padStart(64, '0')}`;

      const sendParam = {
        dstEid: SOLANA_MAINNET_EID,
        to: recipientAddressBytes32 as `0x${string}`,
        amountLD: amountInTokens,
        minAmountLD: amountInTokens,
        extraOptions: '0x' as `0x${string}`,
        composeMsg: '0x' as `0x${string}`,
        oftCmd: '0x' as `0x${string}`,
      };

      // Get current gas prices from Blocknative
      const gasPrice = await fetchGasPrice();

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = ETHEREUM_GAS_LIMIT * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = BigInt(20 * 1e9); // 20 gwei
        nativeFee = ETHEREUM_GAS_LIMIT * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Ethereum has 18 decimals, Solana has 6 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        gasPrice,
        isLoading: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false
      }));
    }
  }, [validateInputs, amount, solanaWallet.publicKey]);

  const executeEthereumToSolana = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      await quoteEthereumToSolana();
      return;
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert amount to proper decimals for Ethereum (18 decimals)
      const amountInTokens = parseUnits(amount, ETHEREUM_TOKEN_DECIMALS);

      // Use custom address if provided, otherwise use connected wallet address
      const recipientSolanaAddress = bridgeState.customSolanaAddress || solanaWallet.publicKey!.toString();
      const solanaAddressBytes = bs58.decode(recipientSolanaAddress);
      const recipientAddressBytes32 = `0x${Buffer.from(solanaAddressBytes).toString('hex').padStart(64, '0')}`;

      const sendParam = {
        dstEid: SOLANA_MAINNET_EID,
        to: recipientAddressBytes32 as `0x${string}`,
        amountLD: amountInTokens,
        minAmountLD: amountInTokens,
        extraOptions: '0x' as `0x${string}`,
        composeMsg: '0x' as `0x${string}`,
        oftCmd: '0x' as `0x${string}`,
      };

      const msgFee = {
        nativeFee: bridgeState.nativeFee,
        lzTokenFee: BigInt(0),
      };

      // Get current gas prices for the transaction
      const gasPrice = await fetchGasPrice();

      // Execute the actual contract transaction with optimized gas
      writeOftContract({
        address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
        abi: oftAbi,
        functionName: 'send',
        args: [sendParam, msgFee, ethAddress as `0x${string}`],
        value: bridgeState.nativeFee,
        gas: ETHEREUM_GAS_LIMIT,
        ...(gasPrice && {
          maxFeePerGas: gasPrice.maxFeePerGas,
          maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas,
        }),
      });

      // The transaction hash will be available in ethTxHash after the transaction is submitted
      // We'll handle the success case in a useEffect that watches for ethTxHash changes

    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteEthereumToSolana, amount, solanaWallet.publicKey, ethAddress, writeOftContract]);

  const canQuote = useMemo(() => {
    return direction === 'sol-to-eth'
      ? solanaWallet.connected && (ethAddress || bridgeState.customEthAddress)
      : isEthConnected && (solanaWallet.publicKey || bridgeState.customSolanaAddress);
  }, [direction, solanaWallet.connected, ethAddress, isEthConnected, solanaWallet.publicKey, bridgeState.customEthAddress, bridgeState.customSolanaAddress]);

  const canExecute = useMemo(() => {
    return canQuote && bridgeState.nativeFee !== null;
  }, [canQuote, bridgeState.nativeFee]);

  const clearTransactionHistory = useCallback(() => {
    setTransactions([]);
  }, []);



  if (!isClient) return null;

  return (
    <div className="w-full">
      {/* Main Bridge Interface */}
      <div className="bg-gray-900 rounded-2xl p-6 shadow-2xl border border-gray-800 backdrop-blur-sm">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">Bridge</h2>
          <p className="text-gray-400 text-sm">Cross-chain token transfers</p>
        </div>

        {/* From Token Section */}
        <div className="mb-1">
          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-3">
              <span className="text-gray-400 text-sm">From</span>
              <span className="text-gray-400 text-sm">
                Balance: {direction === 'sol-to-eth' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.0"
                className="bg-transparent text-white text-2xl font-semibold outline-none flex-1"
                step="0.000001"
                min="0"
              />
              <div className="flex items-center space-x-2 bg-gray-700 rounded-lg px-3 py-2 flex-shrink-0">
                <img
                  src={direction === 'sol-to-eth' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                  alt={direction === 'sol-to-eth' ? 'Solana' : 'Ethereum'}
                  className="w-5 h-5"
                />
                <img
                  src="https://raw.githubusercontent.com/crypto0nsolana/Crypto0nSolana/refs/heads/main/resized_transparent_coin.png"
                  alt="Crypto Coin"
                  className="w-5 h-5"
                />
                <span className="text-white font-medium text-sm">crypto</span>
              </div>
            </div>
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex justify-center my-4">
          <button
            onClick={() => {
              setDirection(direction === 'sol-to-eth' ? 'eth-to-sol' : 'sol-to-eth');
              resetBridgeState();
            }}
            className="bg-gray-700 hover:bg-gray-600 rounded-full p-3 transition-colors border border-gray-600"
          >
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
            </svg>
          </button>
        </div>

        {/* To Token Section */}
        <div className="mb-6">
          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-3">
              <span className="text-gray-400 text-sm">To</span>
              <span className="text-gray-400 text-sm">
                Balance: {direction === 'eth-to-sol' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-white text-2xl font-semibold">
                {bridgeState.receiveAmount || '0.0'}
              </div>
              <div className="flex items-center space-x-2 bg-gray-700 rounded-lg px-3 py-2 flex-shrink-0">
                <img
                  src={direction === 'eth-to-sol' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                  alt={direction === 'eth-to-sol' ? 'Solana' : 'Ethereum'}
                  className="w-5 h-5"
                />
                <img
                  src="https://raw.githubusercontent.com/crypto0nsolana/Crypto0nSolana/refs/heads/main/resized_transparent_coin.png"
                  alt="Crypto Coin"
                  className="w-5 h-5"
                />
                <span className="text-white font-medium text-sm">crypto</span>
              </div>
            </div>
          </div>
        </div>

        {/* Wallet Connection Section */}
        <div className="mb-6 space-y-3">
          {/* Solana Wallet */}
          <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg border border-gray-700">
            <div className="flex items-center space-x-3">
              <img src="/solana-sol-logo.svg" alt="Solana" className="w-6 h-6" />
              <span className="text-white font-medium">Solana</span>
            </div>
            {solanaWallet.connected ? (
              <div className="flex items-center space-x-2">
                <span className="text-green-400 text-sm">
                  {solanaWallet.publicKey?.toString().slice(0, 4)}...{solanaWallet.publicKey?.toString().slice(-4)}
                </span>
                <button
                  onClick={() => solanaWallet.disconnect()}
                  className="text-gray-400 hover:text-white text-sm"
                >
                  Disconnect
                </button>
              </div>
            ) : (
              <WalletMultiButton className="!bg-purple-600 !hover:bg-purple-700 !text-white !text-sm !py-1 !px-3 !rounded-lg" />
            )}
          </div>

          {/* Ethereum Wallet */}
          <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg border border-gray-700">
            <div className="flex items-center space-x-3">
              <img src="/ethereum-eth-logo.svg" alt="Ethereum" className="w-6 h-6" />
              <span className="text-white font-medium">Ethereum</span>
            </div>
            {isEthConnected ? (
              <div className="flex items-center space-x-2">
                <span className="text-green-400 text-sm">
                  {ethAddress?.slice(0, 6)}...{ethAddress?.slice(-4)}
                </span>
                <button
                  onClick={() => disconnect()}
                  className="text-gray-400 hover:text-white text-sm"
                >
                  Disconnect
                </button>
              </div>
            ) : (
              <EthereumConnectButton />
            )}
          </div>
        </div>

        {/* Custom Address Inputs */}
        {direction === 'sol-to-eth' && !ethAddress && (
          <div className="mb-4">
            <input
              type="text"
              value={bridgeState.customEthAddress}
              onChange={(e) => setBridgeState(prev => ({ ...prev, customEthAddress: e.target.value }))}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
              placeholder="Enter Ethereum address (0x...)"
            />
            <p className="mt-2 text-xs text-gray-400">
              Enter an Ethereum address to receive tokens
            </p>
          </div>
        )}

        {direction === 'eth-to-sol' && !solanaWallet.publicKey && (
          <div className="mb-4">
            <input
              type="text"
              value={bridgeState.customSolanaAddress}
              onChange={(e) => setBridgeState(prev => ({ ...prev, customSolanaAddress: e.target.value }))}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
              placeholder="Enter Solana address..."
            />
            <p className="mt-2 text-xs text-gray-400">
              Enter a Solana address to receive tokens
            </p>
          </div>
        )}

        {/* Quote Display */}
        {bridgeState.nativeFee && (
          <div className="mb-4 p-4 bg-gray-800 rounded-lg border border-gray-700 space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Bridge Fee:</span>
              <span className="text-white font-medium">
                {direction === 'sol-to-eth'
                  ? `${(Number(bridgeState.nativeFee) / 1e9).toFixed(6)} SOL`
                  : `${(Number(bridgeState.nativeFee) / 1e18).toFixed(6)} ETH`
                }
              </span>
            </div>
            {direction === 'eth-to-sol' && bridgeState.gasPrice && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Gas Price:</span>
                <span className="text-white font-medium">
                  {(Number(bridgeState.gasPrice.maxFeePerGas) / 1e9).toFixed(2)} gwei
                </span>
              </div>
            )}
            {bridgeState.receiveAmount && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">You will receive:</span>
                <span className="text-white font-medium">
                  {bridgeState.receiveAmount} crypto
                </span>
              </div>
            )}
            {/* Recipient Address Display */}
            {direction === 'sol-to-eth' && (bridgeState.customEthAddress || ethAddress) && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Recipient:</span>
                <span className="text-white font-mono text-xs">
                  {(bridgeState.customEthAddress || ethAddress)?.slice(0, 6)}...{(bridgeState.customEthAddress || ethAddress)?.slice(-4)}
                </span>
              </div>
            )}
            {direction === 'eth-to-sol' && (bridgeState.customSolanaAddress || solanaWallet.publicKey) && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Recipient:</span>
                <span className="text-white font-mono text-xs">
                  {(bridgeState.customSolanaAddress || solanaWallet.publicKey?.toString())?.slice(0, 6)}...{(bridgeState.customSolanaAddress || solanaWallet.publicKey?.toString())?.slice(-4)}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {bridgeState.error && (
          <div className="mb-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
            <p className="text-sm text-red-400">{bridgeState.error}</p>
          </div>
        )}

        {/* Success Display */}
        {bridgeState.txHash && (
          <div className="mb-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg space-y-2">
            <p className="text-sm text-green-400">
              Transaction sent! Hash: {bridgeState.txHash.slice(0, 8)}...
            </p>
            {bridgeState.layerZeroScanLink && (
              <a
                href={bridgeState.layerZeroScanLink}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-400 hover:text-blue-300 underline block"
              >
                View on LayerZero Scan ↗
              </a>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          {direction === 'sol-to-eth' && (
            <>
              <button
                onClick={quoteSolanaToEthereum}
                disabled={!canQuote || bridgeState.isLoading}
                className="w-full py-4 px-6 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center space-x-2"
              >
                {bridgeState.isLoading && (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                <span>{bridgeState.isLoading ? 'Getting Quote...' : 'Get Quote'}</span>
              </button>

              <button
                onClick={executeSolanaToEthereum}
                disabled={!canExecute || bridgeState.isLoading}
                className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center space-x-2"
              >
                {bridgeState.isLoading && (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                <span>{bridgeState.isLoading ? 'Sending...' : 'Bridge Tokens'}</span>
              </button>
            </>
          )}

          {direction === 'eth-to-sol' && (
            <>
              <button
                onClick={quoteEthereumToSolana}
                disabled={!canQuote || bridgeState.isLoading}
                className="w-full py-4 px-6 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center space-x-2"
              >
                {bridgeState.isLoading && (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                <span>{bridgeState.isLoading ? 'Getting Quote...' : 'Get Quote'}</span>
              </button>

              <button
                onClick={executeEthereumToSolana}
                disabled={!canExecute || bridgeState.isLoading}
                className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center space-x-2"
              >
                {bridgeState.isLoading && (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                <span>{bridgeState.isLoading ? 'Sending...' : 'Bridge Tokens'}</span>
              </button>
            </>
          )}
        </div>
      </div>

      <TransactionStatus
        transactions={transactions}
        onClearHistory={clearTransactionHistory}
      />
    </div>
  );
}
